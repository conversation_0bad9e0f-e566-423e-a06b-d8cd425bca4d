package retalix

import (
	"context"
	"strings"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/models"
)

func (r *Retalix) MakeAppointment(
	ctx context.Context,
	req models.MakeAppointmentRequest,
	opts ...models.SchedulingOption,
) (models.Appointment, error) {

	options := &models.SchedulingOptions{}
	options.Apply(opts...)

	if options.Warehouse.WarehouseID == "" || options.Warehouse.WarehouseName == "" {
		return models.Appointment{}, &Error{
			Type:    ValidationError,
			Message: "missing warehouse details",
		}
	}

	var poNumbers []string
	if req.PONums != "" {
		if strings.Contains(req.PONums, ",") {
			for _, po := range strings.Split(req.PONums, ",") {
				if trimmed := strings.TrimSpace(po); trimmed != "" {
					poNumbers = append(poNumbers, trimmed)
				}
			}
		} else {
			// Single PO number
			poNumbers = []string{strings.TrimSpace(req.PONums)}
		}
	}

	// Step 1: Initial setup and validation
	err := r.getApptFormViewState(ctx)
	if err != nil {
		return models.Appointment{}, err
	}

	err = r.postApptFormForValidation(ctx, poNumbers, options.Warehouse)
	if err != nil {
		return models.Appointment{}, err
	}

	err = r.beginForceApptFormValidation(ctx, options.Warehouse)
	if err != nil {
		return models.Appointment{}, err
	}

	// Step 2: Validate POs
	_, err = r.getValidatedApptForm(ctx, options.Warehouse)
	if err != nil {
		return models.Appointment{}, err
	}

	// Step 3: Get schedule view
	doc, err := r.getScheduleView(ctx, options.Warehouse)
	if err != nil {
		return models.Appointment{}, err
	}

	// Step 4: Extract viewstate for subsequent requests
	vs := extractViewState(doc)
	if vs.State == "" || vs.Validation == "" || vs.Generator == "" {
		return models.Appointment{}, &Error{
			Type:    ValidationError,
			Message: "schedule view missing required form data",
		}
	}

	// Step 5: Submit reservation with selected appointment date/time
	_, err = r.submitReservation(ctx, req, vs)
	if err != nil {
		return models.Appointment{}, err
	}

	// Step 6: Extract confirmation details
	// Repeat steps 1-4 to get confirmation page
	err = r.getApptFormViewState(ctx)
	if err != nil {
		return models.Appointment{}, err
	}

	err = r.postApptFormForValidation(ctx, poNumbers, options.Warehouse)
	if err != nil {
		return models.Appointment{}, err
	}

	err = r.beginForceApptFormValidation(ctx, options.Warehouse)
	if err != nil {
		return models.Appointment{}, err
	}

	_, err = r.getValidatedApptForm(ctx, options.Warehouse)
	if err != nil {
		return models.Appointment{}, err
	}

	doc, err = r.getScheduleView(ctx, options.Warehouse)
	if err != nil {
		return models.Appointment{}, err
	}

	// Extract confirmation number from validation response
	confirmationDetails, err := r.extractConfirmationFromValidation(doc)
	if err != nil {
		return models.Appointment{}, err
	}

	return models.Appointment{
		ExternalID:          confirmationDetails.ID,
		ConfirmationNo:      confirmationDetails.Number,
		ExternalWarehouseID: req.WarehouseID,
		DockID:              req.DockID,
		PONums:              req.PONums,
		StartTime:           req.StartTime,
		Notes:               req.Notes,
		CcEmails:            req.CcEmails,
		Status:              "confirmed",
	}, nil
}

func (r *Retalix) MakeAppointmentWithLoad(
	_ context.Context,
	_ models.MakeAppointmentRequest,
	_ models.Load,
) (models.Appointment, error) {

	return models.Appointment{}, errtypes.NotImplemented(models.Retalix, "MakeAppointmentWithLoad")
}
