# Postgres DB Docker Image with extensions
# See common/rds/README.md for more info

FROM postgis/postgis:17-3.4

# Install build dependencies and pgvector
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential \
        libpq-dev \
        wget \
        git \
        clang-13 \
        llvm-13 \
        postgresql-server-dev-17 \
    && update-alternatives --install /usr/bin/clang clang /usr/bin/clang-13 100 \
    && update-alternatives --install /usr/bin/llvm-config llvm-config /usr/bin/llvm-config-13 100 \
    && git clone --branch v0.8.0 https://github.com/pgvector/pgvector.git /tmp/pgvector \
    && cd /tmp/pgvector \
    && make \
    && make install \
    && cd / \
    && rm -rf /tmp/pgvector \
    && apt-get purge -y --auto-remove build-essential postgresql-server-dev-17 libpq-dev wget git clang-13 llvm-13 \
    && rm -rf /var/lib/apt/lists/*

