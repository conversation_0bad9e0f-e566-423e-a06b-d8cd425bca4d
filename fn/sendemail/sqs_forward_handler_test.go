package main

import (
	"context"
	"fmt"
	"testing"

	"github.com/go-redis/redismock/v9"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/emails"
	"github.com/drumkitai/drumkit/common/helpers/oauth"
	"github.com/drumkitai/drumkit/common/helpers/sqsclient"
	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient"
	mem "github.com/drumkitai/drumkit/common/integrations/email/gmailclient/mock"
	"github.com/drumkitai/drumkit/common/models"
	rediscommon "github.com/drumkitai/drumkit/common/redis"
)

func TestHandleSQSEvent_GmailForward_Success(t *testing.T) {
	// Save original package-level vars to restore after test
	origGetUserByID := dbFuncGetUserByID
	origGmailClientNew := gmailClientConstructor
	origBatchCreateGeneratedEmails := batchCreateGeneratedEmails
	origSkipDuplicateForward := skipDuplicateForward
	origSkipSelfForward := skipSelfForward
	origRedisRDB := rediscommon.RDB

	defer func() {
		dbFuncGetUserByID = origGetUserByID
		gmailClientConstructor = origGmailClientNew
		batchCreateGeneratedEmails = origBatchCreateGeneratedEmails
		skipDuplicateForward = origSkipDuplicateForward
		skipSelfForward = origSkipSelfForward
		rediscommon.RDB = origRedisRDB
	}()

	newPayload := func() *sqsclient.SendEmailSQSEventBody {
		return &sqsclient.SendEmailSQSEventBody{
			Email: &models.Email{
				Model:        gorm.Model{ID: 123},
				ExternalID:   "external-id-123",
				UserID:       1,
				RFCMessageID: "rfc-id-abc",
				ThreadID:     "thread-xyz",
				Body:         "hello world",
				BodyType:     models.MarkdownEmailBodyType,
			},
			Rule: &models.EmailForwardingRule{
				Model:                           gorm.Model{ID: 99},
				Recipients:                      []string{"<EMAIL>"},
				CCRecipients:                    []string{"<EMAIL>"},
				UseSenderSignature:              true,
				AddLabels:                       []string{"label1", "label2"},
				ArchiveOriginalForwardedEmail:   true,
				ArchiveSubsequentEmailsInThread: false,
			},
			WasThreadForwardedBefore: false,
		}
	}

	newMockGmailClient := func() *mem.Service {
		return &mem.Service{}
	}

	setupDefaultMocks := func(
		mockGmailClient *mem.Service,
		createdEmailsPtr *[]*models.GeneratedEmail,
	) {
		dbFuncGetUserByID = func(_ context.Context, userID uint) (models.User, error) {
			return models.User{
				Model:         gorm.Model{ID: userID},
				EmailAddress:  "<EMAIL>",
				EmailProvider: models.GmailEmailProvider,
				ServiceID:     42,
			}, nil
		}
		gmailClientConstructor = func(
			context.Context,
			string,
			string,
			*models.User,
			...oauth.Option,
		) (gmailclient.Client, error) {
			return mockGmailClient, nil
		}
		batchCreateGeneratedEmails = func(_ context.Context, emails []*models.GeneratedEmail) error {
			if createdEmailsPtr != nil {
				*createdEmailsPtr = emails
			}
			return nil
		}
		skipDuplicateForward = func(context.Context, string, uint) (bool, error) { return false, nil }
		skipSelfForward = func(context.Context, uint, string, uint) (bool, error) { return false, nil }
	}

	t.Run("success", func(t *testing.T) {
		payload := newPayload()
		var createdEmails []*models.GeneratedEmail

		mockGmailClient := newMockGmailClient()
		setupDefaultMocks(mockGmailClient, &createdEmails)
		mockRedis := setupMockRedis(payload)

		// --- Run ---
		err := handleSQSEvent(context.Background(), payload)
		assert.NoError(t, err)
		assert.Len(t, createdEmails, 1)
		assert.NoError(t, mockRedis.ExpectationsWereMet())
		assert.Equal(t, 3, len(mockGmailClient.Calls))
		assert.ElementsMatch(
			t,
			[]string{
				"mem.ForwardMessage()",
				"mem.AddLabels(generated-external-id-1395, [label1, label2])",
				"mem.ArchiveMessage()",
			},
			mockGmailClient.Calls,
		)
	})

	t.Run("gmail_error", func(t *testing.T) {
		payload := newPayload()
		payload.Email.ExternalID = "force error"
		var createdEmails []*models.GeneratedEmail

		mockGmailClient := newMockGmailClient()
		setupDefaultMocks(mockGmailClient, &createdEmails)

		// Redis
		mockRDB, mockRedis := redismock.NewClientMock()
		rediscommon.RDB = mockRDB
		forwardedMessageKey := emails.GetRedisKeyForwardedMessage(payload.Rule.ID, payload.Email.ExternalID)
		mockRedis.ExpectSet(forwardedMessageKey, []byte("\"true\""), emails.ForwardedMessageTTL).SetVal("OK")
		mockRedis.ExpectDel(forwardedMessageKey).SetVal(1)

		// --- Run ---
		err := handleSQSEvent(context.Background(), payload)
		assert.Error(t, err)
		assert.NoError(t, mockRedis.ExpectationsWereMet())
		assert.Equal(t, 1, len(mockGmailClient.Calls), "calls: %v", mockGmailClient.Calls)
		assert.Equal(t, "mem.ForwardMessage()", mockGmailClient.Calls[0], "calls: %v", mockGmailClient.Calls)
		assert.Len(t, createdEmails, 0)
	})

	t.Run("is_duplicate", func(t *testing.T) {
		payload := newPayload()
		var createdEmails []*models.GeneratedEmail

		mockGmailClient := newMockGmailClient()
		setupDefaultMocks(mockGmailClient, &createdEmails)
		skipDuplicateForward = func(context.Context, string, uint) (bool, error) { return true, nil }

		mockRDB, mockRedis := redismock.NewClientMock()
		// No expecations (technically redis used in skip funcs) but test injects those at top level
		rediscommon.RDB = mockRDB

		// --- Run ---
		err := handleSQSEvent(context.Background(), payload)
		assert.NoError(t, err)
		assert.NoError(t, mockRedis.ExpectationsWereMet())
		assert.Equal(t, 0, len(mockGmailClient.Calls))
		assert.Len(t, createdEmails, 0)
	})

	t.Run("no archive", func(t *testing.T) {
		payload := newPayload()
		payload.Rule.ArchiveOriginalForwardedEmail = false
		payload.Rule.ArchiveSubsequentEmailsInThread = false
		var createdEmails []*models.GeneratedEmail

		mockGmailClient := newMockGmailClient()
		setupDefaultMocks(mockGmailClient, &createdEmails)

		mockRedis := setupMockRedis(payload)

		// --- Run ---
		err := handleSQSEvent(context.Background(), payload)
		assert.NoError(t, err)
		assert.Len(t, createdEmails, 1)
		assert.NoError(t, mockRedis.ExpectationsWereMet())
		assert.Equal(t, 2, len(mockGmailClient.Calls))
		assert.ElementsMatch(
			t,
			[]string{
				"mem.ForwardMessage()",
				"mem.AddLabels(generated-external-id-1395, [label1, label2])",
			},
			mockGmailClient.Calls,
		)
	})
}

func setupMockRedis(payload *sqsclient.SendEmailSQSEventBody) redismock.ClientMock {
	mockRDB, mockRedis := redismock.NewClientMock()
	rediscommon.RDB = mockRDB
	forwardedMessageKey := emails.GetRedisKeyForwardedMessage(payload.Rule.ID, payload.Email.ExternalID)
	forwardedThreadKey := emails.GetRedisKeyForwardedThread(payload.Rule.ID, payload.Email.ThreadID)

	// Set by mockGmailClient.ForwardMessage
	generatedRFC := "generated-rfc-089"
	generatedExternalID := "generated-external-id-1395"
	forwardedRFCKey := emails.GetRedisKeyForwardedRFC(payload.Rule.ID, generatedRFC)

	// Set up basic expectations for SetKeyWithRetries and SetKey
	mockRedis.ExpectSet(forwardedMessageKey, []byte("\"true\""), emails.ForwardedMessageTTL).SetVal("OK")
	mockRedis.ExpectSet(
		forwardedThreadKey,
		[]byte(fmt.Sprintf("\"%s\"", payload.Email.ExternalID)),
		emails.ForwardedMessageTTL,
	).SetVal("OK")
	mockRedis.ExpectSet(
		forwardedRFCKey,
		[]byte(fmt.Sprintf("\"%s\"", generatedExternalID)),
		emails.ForwardedMessageTTL,
	).SetVal("OK")
	return mockRedis
}
