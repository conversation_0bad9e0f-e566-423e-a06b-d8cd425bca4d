package appt

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/integrations/scheduling"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	apptDB "github.com/drumkitai/drumkit/common/rds/appt"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	warehouseDB "github.com/drumkitai/drumkit/common/rds/warehouses"
)

type (
	BodyV2 struct {
		LoadID            uint   `json:"loadID" validate:"required"`
		FreightTrackingID string `json:"freightTrackingID" validate:"required"`
		Source            string `json:"source"` // TODO: limit type to scheduling integrations
		// If DryRun=true, the appt is created in the DB but not submitted to Scheduler or TMS
		// Useful for testing appointment creation without spamming Scheduler with appointment reservations
		DryRun bool `json:"dryRun"`

		StopType          string    `json:"stopType" validate:"required,oneof=pickup dropoff"`
		StartTime         time.Time `json:"start" validate:"required"`
		WarehouseID       string    `json:"warehouseID" validate:"required"`
		WarehouseTimezone string    `json:"warehouseTimezone" validate:"required"` // IANA, e.g. America/New_York
		DockID            string    `json:"dockId" validate:"required"`
		LoadTypeID        string    `json:"loadTypeId" validate:"required"`
		SubscribedEmail   string    `json:"subscribedEmail"`
		CcEmails          []string  `json:"ccEmails"`
		Operation         string    `json:"operation,omitempty"`
		Company           string    `json:"company,omitempty"`
		TrailerType       string    `json:"trailerType,omitempty"`
		Notes             string    `json:"notes"`
		PONums            string    `json:"poNums"`
		// Opendock-specific reference number, required by some warehouses
		RefNumber string `json:"refNumber"`
		// Contact information
		Phone string `json:"phone"`
		Email string `json:"email"`
		models.CustomApptFieldsTemplate
		// E2open-specific request type
		RequestType     models.RequestType `json:"requestType"`
		AppointmentType string             `json:"appointmentType"`
		// For bulk appointment support
		Appointments []models.AppointmentData `json:"appointments,omitempty"`
	}

	ResponseV2 struct {
		models.Appointment
		IsAppointmentTMSUpdateEnabled bool `json:"isAppointmentTMSUpdateEnabled"`
		TMSUpdateSucceeded            bool `json:"tmsUpdateSucceeded"`
		// User-facing message, typically used if creating the Scheduler appt succeeded but not the TMS update
		Message string `json:"message"`
	}
)

// appointmentHandler defines the interface for handling different scheduling integrations.
type appointmentHandler func(context.Context, *fiber.Ctx, models.User, *BodyV2) error

// handlers maps scheduling integration names to their handlers.
var handlers = map[string]appointmentHandler{
	string(models.OneNetwork): handleOneNetwork,
	string(models.Opendock):   handleOpendock,
	string(models.Retalix):    handleRetalix,
	string(models.E2open):     handleE2open,
	string(models.YardView):   handleYardView,
}

// MakeV2 handles appointment creation for various scheduling integrations.
func MakeV2(c *fiber.Ctx) error {
	var body BodyV2
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	// Default to Opendock if source is empty.
	if body.Source == "" {
		body.Source = string(models.Opendock)
	}

	email := middleware.ClaimsFromContext(c).Email
	userID := middleware.UserIDFromContext(c)

	ctx := log.With(
		c.UserContext(),
		zap.String("emailAddress", email),
		zap.Uint("userID", userID),
		zap.String("integration", body.Source),
		zap.Any("requestBody", body),
	)

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "GetUserByID failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	body.CcEmails = append(body.CcEmails, body.SubscribedEmail)

	// Internal emails are not allowed to schedule real PROs.
	if perms.IsInternalEmail(ctx, email) && body.Source == string(models.Opendock) && !body.DryRun {
		log.Error(ctx, "Internal email not allowed to schedule production load")
		return c.Status(http.StatusForbidden).JSON(fiber.Map{"error": "Not allowed to schedule production load"})
	}

	handler, ok := handlers[body.Source]
	if !ok {
		log.Warn(ctx, "unsupported scheduling integration")
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{"error": "unsupported scheduling integration"})
	}

	return handler(ctx, c, user, &body)
}

// handleOpendock processes Opendock appointments with TMS updates.
func handleOpendock(ctx context.Context, c *fiber.Ctx, user models.User, body *BodyV2) error {
	tmsIntegration, err := getTMSIntegration(ctx, user.ServiceID, body.FreightTrackingID)
	if err != nil {
		log.Error(ctx, "Failed to get TMS integration", zap.Error(err))
		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to get TMS integration"})
	}

	tmsClient, err := tms.New(ctx, *tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return createAndUpdate(ctx, c, user, body, tmsIntegration, tmsClient, nil)
}

// handleRetalix processes Retalix appointments without TMS updates.
func handleRetalix(ctx context.Context, c *fiber.Ctx, user models.User, body *BodyV2) error {
	warehouse, err := warehouseDB.GetWarehouseByIDAndSource(
		ctx,
		user.ServiceID,
		models.RetalixSource,
		body.WarehouseID,
	)
	if err != nil {
		log.Error(ctx, "failed to get retalix warehouse", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return createAndUpdate(ctx, c, user, body, nil, nil, warehouse)
}

// handleE2open processes E2open appointments.
func handleE2open(ctx context.Context, c *fiber.Ctx, user models.User, body *BodyV2) error {
	return createAndUpdate(ctx, c, user, body, nil, nil, nil)
}

// handleOneNetwork processes OneNetwork appointments.
func handleOneNetwork(ctx context.Context, c *fiber.Ctx, user models.User, body *BodyV2) error {
	return createAndUpdate(ctx, c, user, body, nil, nil, nil)
}

// handleYardView processes YardView appointments.
func handleYardView(ctx context.Context, c *fiber.Ctx, user models.User, body *BodyV2) error {
	return createAndUpdate(ctx, c, user, body, nil, nil, nil)
}

// createAndUpdate creates an appointment and optionally updates TMS.
func createAndUpdate(
	ctx context.Context,
	c *fiber.Ctx,
	user models.User,
	body *BodyV2,
	tmsIntegration *models.Integration,
	tmsClient tms.Interface,
	warehouse *models.Warehouse,
) error {

	ctx = log.With(ctx, zap.Time("apptTime", body.StartTime), zap.Uint("serviceID", user.ServiceID))

	appt, err := createAppointment(ctx, body, user.EmailAddress, user.ID, user.ServiceID, warehouse)
	if err != nil {
		log.Error(ctx, "failed to create appointment", zap.Error(err))

		var httpErr errtypes.HTTPResponseError
		if errors.As(err, &httpErr) && httpErr.StatusCode == http.StatusConflict {
			return c.Status(http.StatusConflict).JSON(&ResponseV2{
				Message: "An appointment already exists for this reference number. " +
					"Please check existing appointments.",
			})
		}

		var cyclopsErr *models.CyclopsError
		if errors.As(err, &cyclopsErr) {
			return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
				"message": cyclopsErr.Message,
				"errors":  cyclopsErr.Errors,
			})
		}

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to create appointment"})
	}

	if err := apptDB.Create(ctx, &appt); err != nil {
		log.Error(ctx, "failed to create appt record in DB", zap.Error(err))
		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to save appointment"})
	}

	resp := ResponseV2{Appointment: appt}

	if body.DryRun || tmsIntegration == nil || tmsClient == nil {
		if body.Source == string(models.Retalix) {
			resp.Message = "Appointment created successfully. Please update your TMS manually."
		}

		return c.Status(http.StatusOK).JSON(resp)
	}

	service, err := rds.GetServiceByID(ctx, user.ServiceID)
	if err != nil || !service.IsAppointmentTMSUpdateEnabled {
		if err != nil {
			log.Warn(ctx, "Failed to get service feature flag", zap.Error(err))
		}
		return c.Status(http.StatusOK).JSON(resp)
	}

	resp.IsAppointmentTMSUpdateEnabled = true
	if err := updateTMS(ctx, body, &appt, tmsIntegration, tmsClient); err != nil {
		log.Error(ctx, "Failed to update TMS", zap.Error(err))
		return c.Status(http.StatusOK).JSON(resp)
	}

	resp.TMSUpdateSucceeded = true
	return c.Status(http.StatusOK).JSON(resp)
}

// createAppointment creates an appointment, either dry-run or real.
func createAppointment(
	ctx context.Context,
	body *BodyV2,
	email string,
	userID,
	serviceID uint,
	warehouse *models.Warehouse,
) (models.Appointment, error) {

	if body.DryRun {
		return models.Appointment{
			Account:             email,
			UserID:              userID,
			FreightTrackingID:   body.FreightTrackingID,
			LoadID:              body.LoadID,
			ExternalID:          strconv.Itoa(-int(time.Now().Unix())),
			ConfirmationNo:      strconv.Itoa(-int(time.Now().Unix())),
			ExternalWarehouseID: body.WarehouseID,
			LoadTypeID:          body.LoadTypeID,
			DockID:              body.DockID,
			PONums:              body.PONums,
			RefNumber:           body.RefNumber,
			Date:                body.StartTime.Format(time.DateOnly),
			StartTime:           body.StartTime,
			Status:              "{drumkitDryRun: true}",
		}, nil
	}

	integration, err := integrationDB.GetSchedulerByServiceUserIDsAndName(ctx, userID, serviceID, body.Source)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("get scheduler integration: %w", err)
	}

	ctx = log.With(
		ctx,
		zap.String("schedulingUsername", integration.Username),
		zap.Uint("integrationID", integration.ID),
	)

	client, err := scheduling.GetCachedClient(ctx, integration)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("create scheduler client: %w", err)
	}

	req := models.MakeAppointmentRequest{
		CcEmails:     body.CcEmails,
		CustomFields: body.CustomApptFieldsTemplate,
		DockID:       body.DockID,
		LoadTypeID:   body.LoadTypeID,
		Notes:        body.Notes,
		RefNumber:    body.RefNumber,
		StartTime:    body.StartTime,
		ContactDetails: models.ContactDetails{
			Phone: body.Phone,
			Email: body.Email,
		},
	}

	var opts []models.SchedulingOption

	switch body.Source {
	case string(models.E2open):
		req.RequestType = string(body.RequestType)
		req.AppointmentType = body.AppointmentType
		req.LoadTypeID = body.FreightTrackingID
		req.Appointments = body.Appointments
		req.StopType = body.StopType
		req.Company = body.Company
		req.Operation = body.Operation

	case string(models.OneNetwork):
		req.PONums = body.PONums
		req.LoadTypeID = body.FreightTrackingID

	case string(models.Retalix):
		req.PONums = body.PONums
		opts = append(
			opts,
			models.WithWarehouse(
				models.Warehouse{
					WarehouseID:   warehouse.WarehouseID,
					WarehouseName: warehouse.WarehouseName,
				},
			),
		)

	case string(models.YardView):
		req.RequestType = string(body.RequestType)
		req.WarehouseID = body.WarehouseID
	}

	appt, err := client.MakeAppointment(ctx, req, opts...)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("make appointment: %w", err)
	}

	appt.Account = email
	appt.UserID = userID
	if body.Source == string(models.E2open) {
		appt.FreightTrackingID = body.Appointments[0].FreightTrackingID
	} else {
		appt.FreightTrackingID = body.FreightTrackingID
	}
	appt.LoadID = body.LoadID
	appt.ServiceID = serviceID
	appt.Source = models.IntegrationName(body.Source)
	appt.DockID = body.DockID
	appt.ExternalWarehouseID = body.WarehouseID

	if body.Source == string(models.E2open) {
		// TODO: Update these things according to multiple appointments
		rfc3339Layout := "2006-01-02T15:04:05Z07:00" // RFC3339 or use the layout your string matches
		parsedTime, err := time.Parse(rfc3339Layout, body.Appointments[0].Start)
		if err != nil {
			log.Error(ctx, "invalid start time format", zap.Error(err))
			appt.StartTime = body.StartTime
		} else {
			appt.StartTime = parsedTime
		}

		appt.Date = appt.StartTime.Format(time.DateOnly)
	} else {
		appt.Date = body.StartTime.Format(time.DateOnly)
		appt.StartTime = body.StartTime
	}

	if status, err := json.Marshal(appt.Status); err == nil {
		appt.Status = string(status)
	}

	log.Info(
		ctx,
		"appointment created successfully",
		zap.Uint("apptID", appt.ID),
		zap.String("confirmationNo", appt.ConfirmationNo),
	)

	// Try to create warehouse-address association if we have a warehouse
	go func() {
		if warehouse != nil {
			err := createWarehouseAddress(
				context.WithoutCancel(ctx),
				warehouse,
				serviceID,
				body.Source,
				body.RequestType,
			)
			if err != nil {
				log.Error(ctx, "failed to create warehouse-address association", zap.Error(err))
			}
		}
	}()

	return appt, nil
}

// createWarehouseAddress attempts to create a warehouse-address association.
func createWarehouseAddress(
	ctx context.Context,
	warehouse *models.Warehouse,
	serviceID uint,
	integrationName string,
	requestType models.RequestType,
) error {

	if string(requestType) == "" {
		return fmt.Errorf("empty request type for %s integration", integrationName)
	}

	if warehouse == nil {
		return fmt.Errorf("empty warehouse for %s integration", integrationName)
	}

	load, err := loadDB.GetLoadByWarehouseID(
		ctx,
		serviceID,
		warehouse.ID,
		requestType,
	)
	if err != nil {
		return fmt.Errorf("failed to get load: %w", err)
	}

	err = warehouseDB.CreateWarehouseAddress(
		ctx,
		*warehouse,
		load,
		requestType,
	)
	if err != nil {
		return fmt.Errorf("failed to create warehouse-address association: %w", err)
	}

	log.Info(ctx, "successfully created warehouse-address association")
	return nil
}

func getTMSIntegration(ctx context.Context, serviceID uint, freightID string) (*models.Integration, error) {
	load, err := loadDB.GetLoadByFreightIDAndService(ctx, serviceID, freightID)
	if err == nil {
		return &load.TMS, nil
	}

	tms, err := integrationDB.MatchTMSByServiceAndFreightID(ctx, serviceID, freightID)
	if err != nil {
		return nil, fmt.Errorf("could not get tms integration: %w", err)
	}

	return tms, nil
}

// updateTMS updates the TMS with appointment details.
func updateTMS(
	ctx context.Context,
	body *BodyV2,
	appt *models.Appointment,
	tmsIntegration *models.Integration,
	tmsClient tms.Interface,
) error {

	load, _, err := tmsClient.GetLoad(ctx, body.FreightTrackingID)
	if err != nil {
		return fmt.Errorf("get TMS load: %w", err)
	}

	normalizedTime, err := getNormalizedTime(body, *tmsIntegration)
	if err != nil {
		log.Error(ctx, "error loading location", zap.String("timezone", body.WarehouseTimezone), zap.Error(err))
		return fmt.Errorf("normalize time: %w", err)
	}

	reqLoad := load
	updateLoadDetails(&load, &reqLoad, body.StopType, appt.ConfirmationNo, body.Source, normalizedTime)

	updatedLoad, _, err := tmsClient.UpdateLoad(ctx, &load, &reqLoad)
	if err != nil {
		log.Error(ctx, "failed to update TMS load with appt details", zap.Error(err))
		return fmt.Errorf("update TMS load: %w", err)
	}

	if err := loadDB.UpsertLoad(ctx, &updatedLoad, tmsIntegration); err != nil {
		log.Error(ctx, "error updating load DB with appt details", zap.Error(err))
	}

	return nil
}

// getNormalizedTime adjusts the appointment time based on TMS integration requirements.
func getNormalizedTime(body *BodyV2, tmsIntegration models.Integration) (time.Time, error) {
	if tmsIntegration.Name != models.Aljex {
		return body.StartTime, nil
	}

	loc, err := time.LoadLocation(body.WarehouseTimezone)
	if err != nil {
		return time.Time{}, fmt.Errorf("load timezone %s: %w", body.WarehouseTimezone, err)
	}

	// Use .In() in case JSON timestamp had an offset
	t := body.StartTime.In(loc)
	return time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), t.Minute(), t.Second(), t.Nanosecond(), time.UTC), nil
}

// updateLoadDetails updates load fields based on stop type and appointment details.
func updateLoadDetails(curLoad, reqLoad *models.Load, stopType, confirmationNo, integration string, t time.Time) {
	nullTime := models.NullTime{Time: t, Valid: true}
	ref := integration + " " + confirmationNo

	if stopType == "pickup" {
		reqLoad.Pickup.RefNumber = appendWithSeparator(curLoad.Pickup.RefNumber, confirmationNo)
		reqLoad.Pickup.ApptNote = appendWithSeparator(curLoad.Pickup.ApptNote, ref)
		reqLoad.Pickup.ApptStartTime = nullTime
		return
	}

	reqLoad.Consignee.RefNumber = appendWithSeparator(curLoad.Consignee.RefNumber, confirmationNo)
	reqLoad.Consignee.ApptNote = appendWithSeparator(curLoad.Consignee.ApptNote, ref)
	reqLoad.Consignee.ApptStartTime = nullTime
}

func appendWithSeparator(base, addition string) string {
	if base == "" {
		return addition
	}

	return base + " - " + addition
}
